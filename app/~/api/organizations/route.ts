import { asc, count, desc, eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { z } from "zod";
import { withInternalAuth } from "@/core/api/middleware";
import { apiError, apiSuccess } from "@/core/api/response";
import {
	createPaginationMeta,
	paginationSchema,
	validateJsonBody,
	validateSearchParams,
} from "@/core/api/validation";
import { PERMISSIONS } from "@/core/auth/permissions";
import { getDb } from "@/core/database";
import { organizations } from "@/core/database/schema";

// Validation schemas
const createOrganizationSchema = z.object({
	name: z.string().min(1, "Organization name is required").max(255),
	description: z.string().optional(),
});

// GET /~/api/organizations - List organizations
export const GET = withInternalAuth(
	async (request: NextRequest, context) => {
		try {
			const { searchParams } = new URL(request.url);

			// Validate pagination parameters
			const paginationResult = validateSearchParams(
				searchParams,
				paginationSchema,
			);
			if (!paginationResult.success) {
				return apiError(
					"VALIDATION_ERROR",
					"Invalid pagination parameters",
					400,
					{
						fields: paginationResult.errors,
					},
				);
			}

			const { page, limit, sortBy, sortOrder } = paginationResult.data;
			const db = getDb();

			// Build query with organization filtering for data isolation
			const baseQuery = db
				.select()
				.from(organizations)
				.where(eq(organizations.id, context.organizationId));

			// Apply sorting
			const orderColumn =
				sortBy === "name" ? organizations.name : organizations.createdAt;
			const orderDirection =
				sortOrder === "asc" ? asc(orderColumn) : desc(orderColumn);

			// Get total count for pagination
			const [totalResult] = await db
				.select({ count: count() })
				.from(organizations)
				.where(eq(organizations.id, context.organizationId));

			const total = totalResult.count;

			// Get paginated results
			const organizationList = await baseQuery
				.orderBy(orderDirection)
				.limit(limit)
				.offset((page - 1) * limit);

			return apiSuccess(
				organizationList,
				createPaginationMeta(page, limit, total),
			);
		} catch (error) {
			console.error("Error fetching organizations:", error);
			return apiError("INTERNAL_ERROR", "Failed to fetch organizations", 500);
		}
	},
	{
		requirePermissions: [PERMISSIONS.ORGANIZATION_READ],
		requireOrganization: true,
	},
);

// POST /~/api/organizations - Create organization
export const POST = withInternalAuth(
	async (request: NextRequest, _context) => {
		try {
			// Validate request body
			const validation = await validateJsonBody(
				request,
				createOrganizationSchema,
			);
			if (!validation.success) {
				return validation.response;
			}

			const { name, description } = validation.data;
			const db = getDb();

			// Create organization
			const [newOrganization] = await db
				.insert(organizations)
				.values({
					name,
					ownerId: _context.user.id, // Set the current user as the owner
					description,
				})
				.returning();

			return NextResponse.json(
				{
					success: true,
					data: newOrganization,
					meta: {
						timestamp: new Date().toISOString(),
						requestId: crypto.randomUUID(),
					},
				},
				{ status: 201 },
			);
		} catch (error) {
			console.error("Error creating organization:", error);
			return apiError("INTERNAL_ERROR", "Failed to create organization", 500);
		}
	},
	{
		requirePermissions: [PERMISSIONS.ORGANIZATION_MANAGE],
		requireOrganization: false, // Allow creating organizations without existing org membership
	},
);
