import { relations } from "drizzle-orm/relations";
import {
	accounts,
	activityLogs,
	calendarEventAttendees,
	calendarEvents,
	clientBillingAddress,
	clientContacts,
	clientIntegrationAccess,
	clientNotifications,
	clientOrganizations,
	clientPreferences,
	clients,
	contractSignatures,
	contracts,
	contractTemplates,
	documentTemplates,
	expenseCategories,
	fileUploads,
	freelancerProfiles,
	generatedDocuments,
	integrations,
	invoiceItems,
	invoicePayments,
	invoices,
	invoiceTaxes,
	lateFees,
	organizationInvitations,
	organizationMemberPermissions,
	organizationMembers,
	organizations,
	paymentGateways,
	paymentReminders,
	paymentTransactions,
	projectComments,
	projectSprints,
	projects,
	projectTaskAssignees,
	projectTaskAttachments,
	projectTaskComments,
	projectTasks,
	projectTemplates,
	projectTemplateTasks,
	proposalApprovals,
	proposalItems,
	proposals,
	receipts,
	sessions,
	supportTickets,
	taskDependencies,
	taskStatuses,
	taxRates,
	teamInvitations,
	teamMemberPermissions,
	teamMembers,
	teams,
	ticketAttachments,
	ticketMessages,
	twoFactors,
	userOrganizations,
	users,
} from "./schema";

export const activityLogsRelations = relations(activityLogs, ({ one }) => ({
	organization: one(organizations, {
		fields: [activityLogs.organizationId],
		references: [organizations.id],
	}),
	user: one(users, {
		fields: [activityLogs.userId],
		references: [users.id],
	}),
}));

export const organizationsRelations = relations(
	organizations,
	({ one, many }) => ({
		activityLogs: many(activityLogs),
		calendarEvents: many(calendarEvents),
		clientOrganizations: many(clientOrganizations),
		contractTemplates: many(contractTemplates),
		clients: many(clients),
		documentTemplates: many(documentTemplates),
		expenseCategories: many(expenseCategories),
		integrations: many(integrations),
		fileUploads: many(fileUploads),
		organizationInvitations: many(organizationInvitations),
		organizationMembers: many(organizationMembers),
		paymentGateways: many(paymentGateways),
		invoices: many(invoices),
		user: one(users, {
			fields: [organizations.ownerId],
			references: [users.id],
			relationName: "organizations_ownerId_users_id",
		}),
		projectTemplates: many(projectTemplates),
		proposals: many(proposals),
		receipts: many(receipts),
		projects: many(projects),
		taskStatuses: many(taskStatuses),
		taxRates: many(taxRates),
		users: many(users, {
			relationName: "users_organizationId_organizations_id",
		}),
		userOrganizations: many(userOrganizations),
	}),
);

export const usersRelations = relations(users, ({ one, many }) => ({
	activityLogs: many(activityLogs),
	calendarEvents: many(calendarEvents),
	clientIntegrationAccesses: many(clientIntegrationAccess),
	clientNotifications: many(clientNotifications),
	accounts: many(accounts),
	contractTemplates: many(contractTemplates),
	clients: many(clients, {
		relationName: "clients_ownerId_users_id",
	}),
	contracts: many(contracts),
	documentTemplates: many(documentTemplates),
	freelancerProfiles: many(freelancerProfiles),
	generatedDocuments: many(generatedDocuments),
	fileUploads: many(fileUploads),
	organizationInvitations: many(organizationInvitations),
	organizationMembers: many(organizationMembers),
	organizations: many(organizations, {
		relationName: "organizations_ownerId_users_id",
	}),
	projectComments: many(projectComments),
	projectTaskComments: many(projectTaskComments),
	projectTemplates: many(projectTemplates),
	projectTaskAttachments: many(projectTaskAttachments),
	proposals: many(proposals),
	proposalApprovals: many(proposalApprovals),
	receipts: many(receipts),
	projects: many(projects),
	teams: many(teams),
	sessions: many(sessions),
	teamMembers: many(teamMembers),
	ticketMessages: many(ticketMessages),
	supportTickets_assignedTo: many(supportTickets, {
		relationName: "supportTickets_assignedTo_users_id",
	}),
	supportTickets_createdBy: many(supportTickets, {
		relationName: "supportTickets_createdBy_users_id",
	}),
	client: one(clients, {
		fields: [users.clientId],
		references: [clients.id],
		relationName: "users_clientId_clients_id",
	}),
	organization: one(organizations, {
		fields: [users.organizationId],
		references: [organizations.id],
		relationName: "users_organizationId_organizations_id",
	}),
	calendarEventAttendees: many(calendarEventAttendees),
	projectTaskAssignees: many(projectTaskAssignees),
	teamInvitations: many(teamInvitations),
	twoFactors: many(twoFactors),
	userOrganizations: many(userOrganizations),
}));

export const calendarEventsRelations = relations(
	calendarEvents,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [calendarEvents.organizationId],
			references: [organizations.id],
		}),
		user: one(users, {
			fields: [calendarEvents.createdBy],
			references: [users.id],
		}),
		calendarEventAttendees: many(calendarEventAttendees),
	}),
);

export const clientBillingAddressRelations = relations(
	clientBillingAddress,
	({ one }) => ({
		client: one(clients, {
			fields: [clientBillingAddress.clientId],
			references: [clients.id],
		}),
	}),
);

export const clientsRelations = relations(clients, ({ one, many }) => ({
	clientBillingAddresses: many(clientBillingAddress),
	clientContacts: many(clientContacts),
	clientIntegrationAccesses: many(clientIntegrationAccess),
	clientOrganizations: many(clientOrganizations),
	clientPreferences: many(clientPreferences),
	user: one(users, {
		fields: [clients.ownerId],
		references: [users.id],
		relationName: "clients_ownerId_users_id",
	}),
	organization: one(organizations, {
		fields: [clients.organizationId],
		references: [organizations.id],
	}),
	contracts: many(contracts),
	invoices: many(invoices),
	proposals: many(proposals),
	projects: many(projects),
	supportTickets: many(supportTickets),
	users: many(users, {
		relationName: "users_clientId_clients_id",
	}),
}));

export const clientContactsRelations = relations(clientContacts, ({ one }) => ({
	client: one(clients, {
		fields: [clientContacts.clientId],
		references: [clients.id],
	}),
}));

export const clientIntegrationAccessRelations = relations(
	clientIntegrationAccess,
	({ one }) => ({
		client: one(clients, {
			fields: [clientIntegrationAccess.clientId],
			references: [clients.id],
		}),
		integration: one(integrations, {
			fields: [clientIntegrationAccess.integrationId],
			references: [integrations.id],
		}),
		user: one(users, {
			fields: [clientIntegrationAccess.grantedBy],
			references: [users.id],
		}),
	}),
);

export const integrationsRelations = relations(
	integrations,
	({ one, many }) => ({
		clientIntegrationAccesses: many(clientIntegrationAccess),
		organization: one(organizations, {
			fields: [integrations.organizationId],
			references: [organizations.id],
		}),
	}),
);

export const clientNotificationsRelations = relations(
	clientNotifications,
	({ one }) => ({
		user: one(users, {
			fields: [clientNotifications.userId],
			references: [users.id],
		}),
	}),
);

export const clientOrganizationsRelations = relations(
	clientOrganizations,
	({ one }) => ({
		client: one(clients, {
			fields: [clientOrganizations.clientId],
			references: [clients.id],
		}),
		organization: one(organizations, {
			fields: [clientOrganizations.organizationId],
			references: [organizations.id],
		}),
	}),
);

export const clientPreferencesRelations = relations(
	clientPreferences,
	({ one }) => ({
		client: one(clients, {
			fields: [clientPreferences.clientId],
			references: [clients.id],
		}),
	}),
);

export const accountsRelations = relations(accounts, ({ one }) => ({
	user: one(users, {
		fields: [accounts.userId],
		references: [users.id],
	}),
}));

export const contractTemplatesRelations = relations(
	contractTemplates,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [contractTemplates.organizationId],
			references: [organizations.id],
		}),
		user: one(users, {
			fields: [contractTemplates.createdBy],
			references: [users.id],
		}),
		contracts: many(contracts),
	}),
);

export const contractsRelations = relations(contracts, ({ one, many }) => ({
	client: one(clients, {
		fields: [contracts.clientId],
		references: [clients.id],
	}),
	project: one(projects, {
		fields: [contracts.projectId],
		references: [projects.id],
	}),
	contractTemplate: one(contractTemplates, {
		fields: [contracts.templateId],
		references: [contractTemplates.id],
	}),
	user: one(users, {
		fields: [contracts.createdBy],
		references: [users.id],
	}),
	contractSignatures: many(contractSignatures),
}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
	contracts: many(contracts),
	invoices: many(invoices),
	projectSprints: many(projectSprints),
	projectComments: many(projectComments),
	projectTasks: many(projectTasks),
	user: one(users, {
		fields: [projects.ownerId],
		references: [users.id],
	}),
	organization: one(organizations, {
		fields: [projects.organizationId],
		references: [organizations.id],
	}),
	client: one(clients, {
		fields: [projects.clientId],
		references: [clients.id],
	}),
	supportTickets: many(supportTickets),
}));

export const documentTemplatesRelations = relations(
	documentTemplates,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [documentTemplates.organizationId],
			references: [organizations.id],
		}),
		user: one(users, {
			fields: [documentTemplates.createdBy],
			references: [users.id],
		}),
		generatedDocuments: many(generatedDocuments),
	}),
);

export const freelancerProfilesRelations = relations(
	freelancerProfiles,
	({ one }) => ({
		user: one(users, {
			fields: [freelancerProfiles.userId],
			references: [users.id],
		}),
	}),
);

export const generatedDocumentsRelations = relations(
	generatedDocuments,
	({ one }) => ({
		documentTemplate: one(documentTemplates, {
			fields: [generatedDocuments.templateId],
			references: [documentTemplates.id],
		}),
		user: one(users, {
			fields: [generatedDocuments.generatedBy],
			references: [users.id],
		}),
	}),
);

export const invoiceItemsRelations = relations(invoiceItems, ({ one }) => ({
	invoice: one(invoices, {
		fields: [invoiceItems.invoiceId],
		references: [invoices.id],
	}),
}));

export const invoicesRelations = relations(invoices, ({ one, many }) => ({
	invoiceItems: many(invoiceItems),
	invoicePayments: many(invoicePayments),
	invoiceTaxes: many(invoiceTaxes),
	client: one(clients, {
		fields: [invoices.clientId],
		references: [clients.id],
	}),
	project: one(projects, {
		fields: [invoices.projectId],
		references: [projects.id],
	}),
	organization: one(organizations, {
		fields: [invoices.organizationId],
		references: [organizations.id],
	}),
	lateFees: many(lateFees),
	paymentTransactions: many(paymentTransactions),
	paymentReminders: many(paymentReminders),
}));

export const expenseCategoriesRelations = relations(
	expenseCategories,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [expenseCategories.organizationId],
			references: [organizations.id],
		}),
		receipts: many(receipts),
	}),
);

export const invoicePaymentsRelations = relations(
	invoicePayments,
	({ one }) => ({
		invoice: one(invoices, {
			fields: [invoicePayments.invoiceId],
			references: [invoices.id],
		}),
	}),
);

export const invoiceTaxesRelations = relations(invoiceTaxes, ({ one }) => ({
	invoice: one(invoices, {
		fields: [invoiceTaxes.invoiceId],
		references: [invoices.id],
	}),
	taxRate: one(taxRates, {
		fields: [invoiceTaxes.taxRateId],
		references: [taxRates.id],
	}),
}));

export const taxRatesRelations = relations(taxRates, ({ one, many }) => ({
	invoiceTaxes: many(invoiceTaxes),
	organization: one(organizations, {
		fields: [taxRates.organizationId],
		references: [organizations.id],
	}),
}));

export const fileUploadsRelations = relations(fileUploads, ({ one }) => ({
	organization: one(organizations, {
		fields: [fileUploads.organizationId],
		references: [organizations.id],
	}),
	user: one(users, {
		fields: [fileUploads.uploadedBy],
		references: [users.id],
	}),
}));

export const organizationInvitationsRelations = relations(
	organizationInvitations,
	({ one }) => ({
		organization: one(organizations, {
			fields: [organizationInvitations.organizationId],
			references: [organizations.id],
		}),
		user: one(users, {
			fields: [organizationInvitations.invitedById],
			references: [users.id],
		}),
	}),
);

export const organizationMembersRelations = relations(
	organizationMembers,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [organizationMembers.organizationId],
			references: [organizations.id],
		}),
		user: one(users, {
			fields: [organizationMembers.userId],
			references: [users.id],
		}),
		organizationMemberPermissions: many(organizationMemberPermissions),
	}),
);

export const paymentGatewaysRelations = relations(
	paymentGateways,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [paymentGateways.organizationId],
			references: [organizations.id],
		}),
		paymentTransactions: many(paymentTransactions),
	}),
);

export const lateFeesRelations = relations(lateFees, ({ one }) => ({
	invoice: one(invoices, {
		fields: [lateFees.invoiceId],
		references: [invoices.id],
	}),
}));

export const projectSprintsRelations = relations(
	projectSprints,
	({ one, many }) => ({
		project: one(projects, {
			fields: [projectSprints.projectId],
			references: [projects.id],
		}),
		projectTasks: many(projectTasks),
	}),
);

export const paymentTransactionsRelations = relations(
	paymentTransactions,
	({ one }) => ({
		invoice: one(invoices, {
			fields: [paymentTransactions.invoiceId],
			references: [invoices.id],
		}),
		paymentGateway: one(paymentGateways, {
			fields: [paymentTransactions.gatewayId],
			references: [paymentGateways.id],
		}),
	}),
);

export const projectCommentsRelations = relations(
	projectComments,
	({ one, many }) => ({
		project: one(projects, {
			fields: [projectComments.projectId],
			references: [projects.id],
		}),
		user: one(users, {
			fields: [projectComments.userId],
			references: [users.id],
		}),
		projectComment: one(projectComments, {
			fields: [projectComments.parentId],
			references: [projectComments.id],
			relationName: "projectComments_parentId_projectComments_id",
		}),
		projectComments: many(projectComments, {
			relationName: "projectComments_parentId_projectComments_id",
		}),
	}),
);

export const projectTaskCommentsRelations = relations(
	projectTaskComments,
	({ one, many }) => ({
		projectTask: one(projectTasks, {
			fields: [projectTaskComments.taskId],
			references: [projectTasks.id],
		}),
		user: one(users, {
			fields: [projectTaskComments.userId],
			references: [users.id],
		}),
		projectTaskComment: one(projectTaskComments, {
			fields: [projectTaskComments.parentId],
			references: [projectTaskComments.id],
			relationName: "projectTaskComments_parentId_projectTaskComments_id",
		}),
		projectTaskComments: many(projectTaskComments, {
			relationName: "projectTaskComments_parentId_projectTaskComments_id",
		}),
	}),
);

export const projectTasksRelations = relations(
	projectTasks,
	({ one, many }) => ({
		projectTaskComments: many(projectTaskComments),
		projectTaskAttachments: many(projectTaskAttachments),
		project: one(projects, {
			fields: [projectTasks.projectId],
			references: [projects.id],
		}),
		projectSprint: one(projectSprints, {
			fields: [projectTasks.sprintId],
			references: [projectSprints.id],
		}),
		projectTask: one(projectTasks, {
			fields: [projectTasks.parentTaskId],
			references: [projectTasks.id],
			relationName: "projectTasks_parentTaskId_projectTasks_id",
		}),
		projectTasks: many(projectTasks, {
			relationName: "projectTasks_parentTaskId_projectTasks_id",
		}),
		taskStatus: one(taskStatuses, {
			fields: [projectTasks.statusId],
			references: [taskStatuses.id],
		}),
		taskDependencies_taskId: many(taskDependencies, {
			relationName: "taskDependencies_taskId_projectTasks_id",
		}),
		taskDependencies_dependsOnTaskId: many(taskDependencies, {
			relationName: "taskDependencies_dependsOnTaskId_projectTasks_id",
		}),
		projectTaskAssignees: many(projectTaskAssignees),
	}),
);

export const projectTemplatesRelations = relations(
	projectTemplates,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [projectTemplates.organizationId],
			references: [organizations.id],
		}),
		user: one(users, {
			fields: [projectTemplates.createdBy],
			references: [users.id],
		}),
		projectTemplateTasks: many(projectTemplateTasks),
	}),
);

export const projectTaskAttachmentsRelations = relations(
	projectTaskAttachments,
	({ one }) => ({
		projectTask: one(projectTasks, {
			fields: [projectTaskAttachments.taskId],
			references: [projectTasks.id],
		}),
		user: one(users, {
			fields: [projectTaskAttachments.userId],
			references: [users.id],
		}),
	}),
);

export const proposalsRelations = relations(proposals, ({ one, many }) => ({
	client: one(clients, {
		fields: [proposals.clientId],
		references: [clients.id],
	}),
	organization: one(organizations, {
		fields: [proposals.organizationId],
		references: [organizations.id],
	}),
	user: one(users, {
		fields: [proposals.createdBy],
		references: [users.id],
	}),
	proposalApprovals: many(proposalApprovals),
	proposalItems: many(proposalItems),
}));

export const proposalApprovalsRelations = relations(
	proposalApprovals,
	({ one }) => ({
		proposal: one(proposals, {
			fields: [proposalApprovals.proposalId],
			references: [proposals.id],
		}),
		user: one(users, {
			fields: [proposalApprovals.approverUserId],
			references: [users.id],
		}),
	}),
);

export const taskStatusesRelations = relations(
	taskStatuses,
	({ one, many }) => ({
		projectTasks: many(projectTasks),
		organization: one(organizations, {
			fields: [taskStatuses.organizationId],
			references: [organizations.id],
		}),
	}),
);

export const proposalItemsRelations = relations(proposalItems, ({ one }) => ({
	proposal: one(proposals, {
		fields: [proposalItems.proposalId],
		references: [proposals.id],
	}),
}));

export const receiptsRelations = relations(receipts, ({ one }) => ({
	organization: one(organizations, {
		fields: [receipts.organizationId],
		references: [organizations.id],
	}),
	expenseCategory: one(expenseCategories, {
		fields: [receipts.categoryId],
		references: [expenseCategories.id],
	}),
	user: one(users, {
		fields: [receipts.createdBy],
		references: [users.id],
	}),
}));

export const projectTemplateTasksRelations = relations(
	projectTemplateTasks,
	({ one, many }) => ({
		projectTemplate: one(projectTemplates, {
			fields: [projectTemplateTasks.templateId],
			references: [projectTemplates.id],
		}),
		projectTemplateTask: one(projectTemplateTasks, {
			fields: [projectTemplateTasks.parentTaskId],
			references: [projectTemplateTasks.id],
			relationName: "projectTemplateTasks_parentTaskId_projectTemplateTasks_id",
		}),
		projectTemplateTasks: many(projectTemplateTasks, {
			relationName: "projectTemplateTasks_parentTaskId_projectTemplateTasks_id",
		}),
	}),
);

export const taskDependenciesRelations = relations(
	taskDependencies,
	({ one }) => ({
		projectTask_taskId: one(projectTasks, {
			fields: [taskDependencies.taskId],
			references: [projectTasks.id],
			relationName: "taskDependencies_taskId_projectTasks_id",
		}),
		projectTask_dependsOnTaskId: one(projectTasks, {
			fields: [taskDependencies.dependsOnTaskId],
			references: [projectTasks.id],
			relationName: "taskDependencies_dependsOnTaskId_projectTasks_id",
		}),
	}),
);

export const teamsRelations = relations(teams, ({ one, many }) => ({
	user: one(users, {
		fields: [teams.ownerId],
		references: [users.id],
	}),
	teamMembers: many(teamMembers),
	teamInvitations: many(teamInvitations),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
	user: one(users, {
		fields: [sessions.userId],
		references: [users.id],
	}),
}));

export const teamMembersRelations = relations(teamMembers, ({ one, many }) => ({
	team: one(teams, {
		fields: [teamMembers.teamId],
		references: [teams.id],
	}),
	user: one(users, {
		fields: [teamMembers.userId],
		references: [users.id],
	}),
	teamMemberPermissions: many(teamMemberPermissions),
}));

export const ticketAttachmentsRelations = relations(
	ticketAttachments,
	({ one }) => ({
		supportTicket: one(supportTickets, {
			fields: [ticketAttachments.ticketId],
			references: [supportTickets.id],
		}),
		ticketMessage: one(ticketMessages, {
			fields: [ticketAttachments.messageId],
			references: [ticketMessages.id],
		}),
	}),
);

export const supportTicketsRelations = relations(
	supportTickets,
	({ one, many }) => ({
		ticketAttachments: many(ticketAttachments),
		ticketMessages: many(ticketMessages),
		client: one(clients, {
			fields: [supportTickets.clientId],
			references: [clients.id],
		}),
		project: one(projects, {
			fields: [supportTickets.projectId],
			references: [projects.id],
		}),
		user_assignedTo: one(users, {
			fields: [supportTickets.assignedTo],
			references: [users.id],
			relationName: "supportTickets_assignedTo_users_id",
		}),
		user_createdBy: one(users, {
			fields: [supportTickets.createdBy],
			references: [users.id],
			relationName: "supportTickets_createdBy_users_id",
		}),
	}),
);

export const ticketMessagesRelations = relations(
	ticketMessages,
	({ one, many }) => ({
		ticketAttachments: many(ticketAttachments),
		supportTicket: one(supportTickets, {
			fields: [ticketMessages.ticketId],
			references: [supportTickets.id],
		}),
		user: one(users, {
			fields: [ticketMessages.userId],
			references: [users.id],
		}),
	}),
);

export const teamMemberPermissionsRelations = relations(
	teamMemberPermissions,
	({ one }) => ({
		teamMember: one(teamMembers, {
			fields: [teamMemberPermissions.teamMemberId],
			references: [teamMembers.id],
		}),
	}),
);

export const calendarEventAttendeesRelations = relations(
	calendarEventAttendees,
	({ one }) => ({
		calendarEvent: one(calendarEvents, {
			fields: [calendarEventAttendees.eventId],
			references: [calendarEvents.id],
		}),
		user: one(users, {
			fields: [calendarEventAttendees.userId],
			references: [users.id],
		}),
	}),
);

export const contractSignaturesRelations = relations(
	contractSignatures,
	({ one }) => ({
		contract: one(contracts, {
			fields: [contractSignatures.contractId],
			references: [contracts.id],
		}),
	}),
);

export const organizationMemberPermissionsRelations = relations(
	organizationMemberPermissions,
	({ one }) => ({
		organizationMember: one(organizationMembers, {
			fields: [organizationMemberPermissions.organizationMemberId],
			references: [organizationMembers.id],
		}),
	}),
);

export const paymentRemindersRelations = relations(
	paymentReminders,
	({ one }) => ({
		invoice: one(invoices, {
			fields: [paymentReminders.invoiceId],
			references: [invoices.id],
		}),
	}),
);

export const projectTaskAssigneesRelations = relations(
	projectTaskAssignees,
	({ one }) => ({
		projectTask: one(projectTasks, {
			fields: [projectTaskAssignees.taskId],
			references: [projectTasks.id],
		}),
		user: one(users, {
			fields: [projectTaskAssignees.userId],
			references: [users.id],
		}),
	}),
);

export const teamInvitationsRelations = relations(
	teamInvitations,
	({ one }) => ({
		team: one(teams, {
			fields: [teamInvitations.teamId],
			references: [teams.id],
		}),
		user: one(users, {
			fields: [teamInvitations.invitedById],
			references: [users.id],
		}),
	}),
);

export const twoFactorsRelations = relations(twoFactors, ({ one }) => ({
	user: one(users, {
		fields: [twoFactors.userId],
		references: [users.id],
	}),
}));

export const userOrganizationsRelations = relations(
	userOrganizations,
	({ one }) => ({
		user: one(users, {
			fields: [userOrganizations.userId],
			references: [users.id],
		}),
		organization: one(organizations, {
			fields: [userOrganizations.organizationId],
			references: [organizations.id],
		}),
	}),
);
