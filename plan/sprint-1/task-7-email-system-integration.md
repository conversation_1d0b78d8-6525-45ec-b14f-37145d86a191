# Task 7: Email System Integration with Resend

## Task Title

Implement comprehensive email system using Resend with templating and delivery tracking

## Context

Following the platform development guidelines, the email system must use Resend as the primary email service provider with a templating system that allows for customizable email templates. This task establishes the foundation for all platform communications including transactional emails, notifications, and client communications.

The email system must support different email types, proper tracking, delivery status monitoring, and be easily extensible for future requirements. All email templates should be editable through configuration while maintaining proper type safety.

## Development Guidelines

**Configuration Management:**

- Access email configuration through `/core/config.ts` using `getEmailConfig()` helper
- Never read email configuration values directly from environment variables
- Use centralized configuration for all email-related settings

**TypeScript Standards:**

- Strictly prohibit the use of `any` type or `as any` type assertions in email interfaces
- Use proper Resend types and interfaces for all email operations
- Implement proper type guards for email template validation

**Documentation Requirements:**

- Before implementing email features, use Context7 MCP tool to retrieve latest Resend documentation
- Ensure email implementation follows current Resend best practices and API patterns
- Document all email templates and their variable requirements

**Security Standards:**

- Implement proper email validation and sanitization
- Use secure email sending practices with proper error handling
- Ensure email templates cannot be exploited for injection attacks

**Integration Standards:**

- Email system must work seamlessly with the authentication system
- Support both internal user notifications and client communications
- Implement proper email queue management for high-volume scenarios

## Acceptance Criteria

1. **Resend Integration**

   - Resend client properly configured and initialized
   - Email sending functionality with error handling
   - Delivery status tracking and monitoring
   - Rate limiting and quota management

2. **Template System**

   - Configurable email templates for different use cases
   - Variable substitution in templates
   - HTML and text email support
   - Template validation and testing utilities

3. **Email Types Support**

   - Transactional emails (welcome, password reset, etc.)
   - Notification emails (project updates, invoice alerts)
   - Client communication emails
   - System alerts and monitoring emails

4. **Delivery Management**
   - Email queue management for high volume
   - Retry logic for failed deliveries
   - Bounce and complaint handling
   - Email analytics and reporting

## Dependencies

- Task 2: Centralized Configuration and Deployment Setup
- Task 3: Core API Foundation
- Task 4: Better Auth Configuration (for user notifications)
- Resend API account and configuration
- Email templates and design assets
- Database schema for email tracking

## Technical Requirements

### Resend Client Configuration

**File: `core/email/resend.ts`**

```typescript
import { Resend } from "resend";
import { getEmailConfig } from "@/core/config";

let resendClient: Resend | null = null;

export function getResendClient(): Resend {
  if (!resendClient) {
    const config = getEmailConfig();
    resendClient = new Resend(config.apiKey);
  }
  return resendClient;
}

export interface EmailTemplate {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
  replyTo?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Array<{
    filename: string;
    content: string | Buffer;
    contentType?: string;
  }>;
  tags?: Array<{
    name: string;
    value: string;
  }>;
}

export interface EmailResult {
  id: string;
  success: boolean;
  error?: string;
}

export async function sendEmail(template: EmailTemplate): Promise<EmailResult> {
  try {
    const config = getEmailConfig();
    const resend = getResendClient();

    const emailData = {
      from: template.from || `${config.fromName} <${config.fromAddress}>`,
      to: Array.isArray(template.to) ? template.to : [template.to],
      subject: template.subject,
      html: template.html,
      text: template.text,
      reply_to: template.replyTo,
      cc: template.cc
        ? Array.isArray(template.cc)
          ? template.cc
          : [template.cc]
        : undefined,
      bcc: template.bcc
        ? Array.isArray(template.bcc)
          ? template.bcc
          : [template.bcc]
        : undefined,
      attachments: template.attachments,
      tags: template.tags,
    };

    const result = await resend.emails.send(emailData);

    if (result.error) {
      console.error("Failed to send email:", result.error);
      return {
        id: "",
        success: false,
        error: result.error.message,
      };
    }

    return {
      id: result.data!.id,
      success: true,
    };
  } catch (error) {
    console.error("Email sending error:", error);
    return {
      id: "",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Email delivery status checking
export async function getEmailStatus(emailId: string): Promise<{
  status: "sent" | "delivered" | "bounced" | "complained" | "unknown";
  timestamp?: string;
}> {
  try {
    const resend = getResendClient();
    // Note: This would use Resend's webhook or API to check status
    // For now, return a placeholder
    return { status: "sent" };
  } catch (error) {
    console.error("Failed to get email status:", error);
    return { status: "unknown" };
  }
}
```

### Email Template System

**File: `core/email/templates.ts`**

```typescript
import { sendEmail, EmailTemplate } from "./resend";

export const EMAIL_TEMPLATES = {
  // Authentication emails
  WELCOME: "welcome",
  EMAIL_VERIFICATION: "email-verification",
  PASSWORD_RESET: "password-reset",

  // Organization emails
  ORGANIZATION_INVITATION: "organization-invitation",
  MEMBER_ADDED: "member-added",
  ROLE_CHANGED: "role-changed",

  // Client emails
  CLIENT_INVITATION: "client-invitation",
  CLIENT_PORTAL_ACCESS: "client-portal-access",

  // Project emails
  PROJECT_CREATED: "project-created",
  PROJECT_UPDATE: "project-update",
  PROJECT_COMPLETED: "project-completed",
  TASK_ASSIGNED: "task-assigned",

  // Financial emails
  INVOICE_SENT: "invoice-sent",
  INVOICE_REMINDER: "invoice-reminder",
  PAYMENT_RECEIVED: "payment-received",
  PAYMENT_OVERDUE: "payment-overdue",

  // System emails
  SYSTEM_ALERT: "system-alert",
  MAINTENANCE_NOTICE: "maintenance-notice",
} as const;

export type EmailTemplateType =
  (typeof EMAIL_TEMPLATES)[keyof typeof EMAIL_TEMPLATES];

interface TemplateVariables {
  [key: string]: string | number | boolean | undefined;
}

interface EmailTemplateDefinition {
  subject: string;
  html: string;
  text?: string;
  requiredVariables: string[];
}

// Template definitions with variable substitution
const TEMPLATE_DEFINITIONS: Record<EmailTemplateType, EmailTemplateDefinition> =
  {
    [EMAIL_TEMPLATES.WELCOME]: {
      subject: "Welcome to {{organizationName}}!",
      html: `
      <h1>Welcome {{userName}}!</h1>
      <p>Thank you for joining {{organizationName}} on Freelancer Hub.</p>
      <p>You can access your dashboard at: <a href="{{dashboardUrl}}">{{dashboardUrl}}</a></p>
      <p>If you have any questions, feel free to reach out to our support team.</p>
    `,
      text: "Welcome {{userName}}! Thank you for joining {{organizationName}} on Freelancer Hub.",
      requiredVariables: ["userName", "organizationName", "dashboardUrl"],
    },

    [EMAIL_TEMPLATES.EMAIL_VERIFICATION]: {
      subject: "Verify your email address",
      html: `
      <h1>Verify Your Email</h1>
      <p>Hi {{userName}},</p>
      <p>Please verify your email address by clicking the link below:</p>
      <p><a href="{{verificationUrl}}">Verify Email Address</a></p>
      <p>This link will expire in 24 hours.</p>
    `,
      requiredVariables: ["userName", "verificationUrl"],
    },

    [EMAIL_TEMPLATES.PASSWORD_RESET]: {
      subject: "Reset your password",
      html: `
      <h1>Password Reset</h1>
      <p>Hi {{userName}},</p>
      <p>You requested a password reset. Click the link below to reset your password:</p>
      <p><a href="{{resetUrl}}">Reset Password</a></p>
      <p>This link will expire in 1 hour. If you didn't request this, please ignore this email.</p>
    `,
      requiredVariables: ["userName", "resetUrl"],
    },

    [EMAIL_TEMPLATES.ORGANIZATION_INVITATION]: {
      subject: "You've been invited to join {{organizationName}}",
      html: `
      <h1>Organization Invitation</h1>
      <p>Hi {{userName}},</p>
      <p>{{inviterName}} has invited you to join {{organizationName}} as a {{role}}.</p>
      <p><a href="{{invitationUrl}}">Accept Invitation</a></p>
      <p>This invitation will expire in 7 days.</p>
    `,
      requiredVariables: [
        "userName",
        "inviterName",
        "organizationName",
        "role",
        "invitationUrl",
      ],
    },

    [EMAIL_TEMPLATES.CLIENT_INVITATION]: {
      subject: "Access your project portal",
      html: `
      <h1>Client Portal Access</h1>
      <p>Hi {{clientName}},</p>
      <p>{{organizationName}} has set up a client portal for you to track your projects.</p>
      <p><a href="{{portalUrl}}">Access Your Portal</a></p>
      <p>Your login credentials:</p>
      <ul>
        <li>Email: {{clientEmail}}</li>
        <li>Temporary Password: {{temporaryPassword}}</li>
      </ul>
      <p>Please change your password after your first login.</p>
    `,
      requiredVariables: [
        "clientName",
        "organizationName",
        "portalUrl",
        "clientEmail",
        "temporaryPassword",
      ],
    },

    [EMAIL_TEMPLATES.INVOICE_SENT]: {
      subject: "Invoice {{invoiceNumber}} from {{organizationName}}",
      html: `
      <h1>New Invoice</h1>
      <p>Hi {{clientName}},</p>
      <p>You have received a new invoice from {{organizationName}}.</p>
      <p><strong>Invoice Details:</strong></p>
      <ul>
        <li>Invoice Number: {{invoiceNumber}}</li>
        <li>Amount: {{amount}} {{currency}}</li>
        <li>Due Date: {{dueDate}}</li>
      </ul>
      <p><a href="{{invoiceUrl}}">View Invoice</a></p>
    `,
      requiredVariables: [
        "clientName",
        "organizationName",
        "invoiceNumber",
        "amount",
        "currency",
        "dueDate",
        "invoiceUrl",
      ],
    },

    [EMAIL_TEMPLATES.PAYMENT_RECEIVED]: {
      subject: "Payment received - Thank you!",
      html: `
      <h1>Payment Received</h1>
      <p>Hi {{clientName}},</p>
      <p>We've received your payment of {{amount}} {{currency}} for invoice {{invoiceNumber}}.</p>
      <p>Thank you for your business!</p>
      <p><a href="{{receiptUrl}}">Download Receipt</a></p>
    `,
      requiredVariables: [
        "clientName",
        "amount",
        "currency",
        "invoiceNumber",
        "receiptUrl",
      ],
    },

    [EMAIL_TEMPLATES.PROJECT_UPDATE]: {
      subject: "Project Update: {{projectName}}",
      html: `
      <h1>Project Update</h1>
      <p>Hi {{clientName}},</p>
      <p>There's been an update to your project: <strong>{{projectName}}</strong></p>
      <p>{{updateMessage}}</p>
      <p><a href="{{projectUrl}}">View Project</a></p>
    `,
      requiredVariables: [
        "clientName",
        "projectName",
        "updateMessage",
        "projectUrl",
      ],
    },

    [EMAIL_TEMPLATES.SYSTEM_ALERT]: {
      subject: "System Alert: {{alertType}}",
      html: `
      <h1>System Alert</h1>
      <p>Alert Type: {{alertType}}</p>
      <p>Message: {{message}}</p>
      <p>Time: {{timestamp}}</p>
    `,
      requiredVariables: ["alertType", "message", "timestamp"],
    },
  };

// Template rendering with variable substitution
function renderTemplate(
  template: EmailTemplateDefinition,
  variables: TemplateVariables
): {
  subject: string;
  html: string;
  text?: string;
} {
  // Validate required variables
  const missingVariables = template.requiredVariables.filter(
    (variable) => variables[variable] === undefined
  );

  if (missingVariables.length > 0) {
    throw new Error(
      `Missing required variables: ${missingVariables.join(", ")}`
    );
  }

  // Replace variables in template
  const replaceVariables = (text: string): string => {
    return text.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
      const value = variables[variable];
      return value !== undefined ? String(value) : match;
    });
  };

  return {
    subject: replaceVariables(template.subject),
    html: replaceVariables(template.html),
    text: template.text ? replaceVariables(template.text) : undefined,
  };
}

// Main function to send templated emails
export async function sendTemplatedEmail(
  templateType: EmailTemplateType,
  to: string | string[],
  variables: TemplateVariables,
  options?: {
    from?: string;
    replyTo?: string;
    cc?: string | string[];
    bcc?: string | string[];
  }
): Promise<{ id: string; success: boolean; error?: string }> {
  try {
    const template = TEMPLATE_DEFINITIONS[templateType];
    if (!template) {
      throw new Error(`Unknown email template: ${templateType}`);
    }

    const rendered = renderTemplate(template, variables);

    const emailTemplate: EmailTemplate = {
      to,
      subject: rendered.subject,
      html: rendered.html,
      text: rendered.text,
      from: options?.from,
      replyTo: options?.replyTo,
      cc: options?.cc,
      bcc: options?.bcc,
    };

    return await sendEmail(emailTemplate);
  } catch (error) {
    console.error("Failed to send templated email:", error);
    return {
      id: "",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Utility function to validate template variables
export function validateTemplateVariables(
  templateType: EmailTemplateType,
  variables: TemplateVariables
): { valid: boolean; missingVariables: string[] } {
  const template = TEMPLATE_DEFINITIONS[templateType];
  if (!template) {
    return { valid: false, missingVariables: [] };
  }

  const missingVariables = template.requiredVariables.filter(
    (variable) => variables[variable] === undefined
  );

  return {
    valid: missingVariables.length === 0,
    missingVariables,
  };
}
```

## Definition of Done

- [ ] Resend client is properly configured and initialized
- [ ] Email sending functionality works with proper error handling
- [ ] Template system supports variable substitution
- [ ] All required email templates are implemented
- [ ] HTML and text email formats are supported
- [ ] Email delivery status tracking is functional
- [ ] Template validation prevents missing variables
- [ ] Email queue management handles high volume
- [ ] Retry logic handles failed deliveries
- [ ] Email analytics and reporting are available
- [ ] Configuration integrates with centralized config system
- [ ] Type-safe email template definitions
- [ ] Email attachments are supported
- [ ] Rate limiting prevents quota exceeded errors
- [ ] Documentation explains how to add new email templates
