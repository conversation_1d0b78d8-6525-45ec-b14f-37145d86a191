# Task 1: API Documentation and Testing

## Task Title

Create comprehensive API documentation and implement automated testing suite

## Context

As the MVP nears completion, comprehensive API documentation and testing become critical for platform reliability, developer experience, and future maintenance. This task ensures that all API endpoints are properly documented, tested, and ready for production use while establishing patterns for ongoing development.

The documentation will serve both internal development teams and potential third-party integrators, while the testing suite will ensure platform stability and prevent regressions as new features are added.

**Modular Architecture Requirements:**

- Testing infrastructure supports modular architecture with isolated test suites per module
- Documentation reflects the simplified permission system and modular structure
- API documentation covers both `/~/api` (internal) and `/api` (public) route structures
- Testing validates proper permission enforcement and data isolation

## Development Guidelines

**Configuration Management:**

- All testing configuration must be accessed through `/core/config.ts` - never read environment variables directly
- Use centralized configuration for test database, API endpoints, and testing tools
- Configuration validation must be handled by the centralized configuration system

**TypeScript Standards:**

- Strictly prohibit the use of `any` type or `as any` type assertions in all testing code
- Use proper TypeScript interfaces and types for all test data and API responses
- Implement proper type guards for test validation and assertion functions

**Route Structure:**

- Document and test both `/~/api` (internal) and `/api` (public) route structures
- Ensure API documentation reflects the correct route prefixes and permission requirements
- Test route access control and permission enforcement

**Authentication & Authorization:**

- Test simplified permission system: `{module}.read` and `{module}.manage` for all modules
- Validate role-based access control across all API endpoints
- Test both internal user and client user authentication flows

**Documentation Requirements:**

- Before implementing testing features, use Context7 MCP tool to retrieve latest API testing and documentation best practices
- Ensure documentation reflects the simplified permission model and modular architecture
- Document all API endpoints with correct route structures and permission requirements

**Modular Architecture:**

- Testing infrastructure must support modular architecture with isolated test suites
- Each module should have its own test suite that can run independently
- Integration tests should validate proper module interactions
- Documentation should reflect the modular structure and clear module boundaries

## Acceptance Criteria

1. **API Documentation**

   - Complete OpenAPI/Swagger specification for all endpoints
   - Interactive API documentation with examples
   - Authentication and authorization documentation
   - Error response documentation with examples
   - Rate limiting and usage guidelines

2. **Automated Testing**

   - Unit tests for all API endpoints
   - Integration tests for complex workflows
   - Authentication and authorization tests
   - Data validation and error handling tests
   - Performance and load testing basics

3. **Testing Infrastructure**

   - Test database setup and teardown
   - Test data fixtures and factories
   - Continuous integration pipeline
   - Test coverage reporting
   - Automated test execution on deployment

4. **Quality Assurance**
   - API response consistency validation
   - Security testing for common vulnerabilities
   - Data integrity testing across operations
   - Multi-tenant isolation testing
   - Performance benchmarking

## Dependencies

- Sprint 1: All tasks completed
- Sprint 2: Organization and User Management completed
- Sprint 3: Client Management completed
- Sprint 4: Project Management completed
- Sprint 5: Invoice Management completed
- All API endpoints implemented and functional
- Database schema stable and deployed
- Authentication system operational

## Technical Requirements

### API Documentation Structure

**File: `docs/api/openapi.yaml`**

```yaml
openapi: 3.0.3
info:
  title: Freelancer Hub API
  description: Comprehensive freelancer management platform API
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.freelancerhub.com/v1
    description: Production server
  - url: https://staging-api.freelancerhub.com/v1
    description: Staging server
  - url: http://localhost:3000/api/v1
    description: Development server

security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Authentication token from Better Auth

  schemas:
    Error:
      type: object
      required:
        - success
        - error
        - meta
      properties:
        success:
          type: boolean
          example: false
        error:
          type: object
          properties:
            code:
              type: string
              example: "VALIDATION_ERROR"
            message:
              type: string
              example: "Validation failed"
            details:
              type: object
        meta:
          type: object
          properties:
            timestamp:
              type: string
              format: date-time
            requestId:
              type: string
              format: uuid

    ApiResponse:
      type: object
      required:
        - success
        - data
        - meta
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
        meta:
          type: object
          properties:
            timestamp:
              type: string
              format: date-time
            requestId:
              type: string
              format: uuid
            pagination:
              type: object
              properties:
                page:
                  type: integer
                limit:
                  type: integer
                total:
                  type: integer
                totalPages:
                  type: integer

    Organization:
      type: object
      required:
        - id
        - name
        - isActive
        - createdAt
        - updatedAt
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          maxLength: 255
        description:
          type: string
          maxLength: 1000
        website:
          type: string
          format: uri
        industry:
          type: string
          maxLength: 100
        size:
          type: string
          enum: ["1-10", "11-50", "51-200", "201-500", "500+"]
        timezone:
          type: string
          default: "UTC"
        currency:
          type: string
          minLength: 3
          maxLength: 3
          default: "USD"
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        userRole:
          type: string
          enum: ["owner", "admin", "member", "viewer"]

paths:
  /health:
    get:
      summary: Health check
      description: Check API health and service status
      tags:
        - Health
      security: []
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          status:
                            type: string
                            example: "healthy"
                          version:
                            type: string
                            example: "1.0.0"
                          services:
                            type: object
                            properties:
                              database:
                                type: string
                                example: "connected"
                              auth:
                                type: string
                                example: "operational"

  /organizations:
    get:
      summary: List organizations
      description: Get list of organizations the user belongs to
      tags:
        - Organizations
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        "200":
          description: List of organizations
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/Organization"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

    post:
      summary: Create organization
      description: Create a new organization
      tags:
        - Organizations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  minLength: 1
                  maxLength: 255
                description:
                  type: string
                  maxLength: 1000
                website:
                  type: string
                  format: uri
                industry:
                  type: string
                  maxLength: 100
                size:
                  type: string
                  enum: ["1-10", "11-50", "51-200", "201-500", "500+"]
                timezone:
                  type: string
                  default: "UTC"
                currency:
                  type: string
                  minLength: 3
                  maxLength: 3
                  default: "USD"
      responses:
        "201":
          description: Organization created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/Organization"
        "400":
          description: Validation error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
```

### Testing Infrastructure

**File: `tests/setup.ts`**

```typescript
import { beforeAll, afterAll, beforeEach, afterEach } from "vitest";
import { getDb } from "@/core/database";
import { sql } from "drizzle-orm";
import { auth } from "@/core/auth/server";

// Test database setup
let testDb: ReturnType<typeof getDb>;

beforeAll(async () => {
  // Initialize test database
  testDb = getDb();

  // Run migrations
  await testDb.execute(sql`
    CREATE SCHEMA IF NOT EXISTS test_schema;
    SET search_path TO test_schema;
  `);
});

afterAll(async () => {
  // Clean up test database
  await testDb.execute(sql`DROP SCHEMA IF EXISTS test_schema CASCADE;`);
});

beforeEach(async () => {
  // Start transaction for test isolation
  await testDb.execute(sql`BEGIN;`);
});

afterEach(async () => {
  // Rollback transaction
  await testDb.execute(sql`ROLLBACK;`);
});

// Test utilities
export async function createTestUser(userData: {
  email: string;
  name: string;
  userType?: "internal" | "client";
  organizationId?: string;
}) {
  return await auth.api.signUpEmail({
    email: userData.email,
    password: "test-password-123",
    name: userData.name,
    body: {
      userType: userData.userType || "internal",
      organizationId: userData.organizationId,
    },
  });
}

export async function createTestOrganization(ownerId: string) {
  const organizationId = crypto.randomUUID();

  await testDb.insert(organizations).values({
    id: organizationId,
    ownerId,
    name: "Test Organization",
    timezone: "UTC",
    currency: "USD",
  });

  return organizationId;
}

export async function getAuthHeaders(userId: string) {
  const session = await auth.api.createSession({
    userId,
    expiresIn: 60 * 60, // 1 hour
  });

  return {
    Authorization: `Bearer ${session.token}`,
  };
}
```

**File: `tests/api/organizations.test.ts`**

```typescript
import { describe, it, expect, beforeEach } from "vitest";
import { testClient } from "../utils/test-client";
import {
  createTestUser,
  createTestOrganization,
  getAuthHeaders,
} from "../setup";

describe("Organizations API", () => {
  let testUser: any;
  let authHeaders: Record<string, string>;

  beforeEach(async () => {
    testUser = await createTestUser({
      email: "<EMAIL>",
      name: "Test User",
    });
    authHeaders = await getAuthHeaders(testUser.user.id);
  });

  describe("GET /api/v1/organizations", () => {
    it("should return user's organizations", async () => {
      const organizationId = await createTestOrganization(testUser.user.id);

      const response = await testClient
        .get("/api/v1/organizations")
        .set(authHeaders);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].id).toBe(organizationId);
      expect(response.body.data[0].userRole).toBe("owner");
    });

    it("should return empty array for user with no organizations", async () => {
      const response = await testClient
        .get("/api/v1/organizations")
        .set(authHeaders);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(0);
    });

    it("should require authentication", async () => {
      const response = await testClient.get("/api/v1/organizations");

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("UNAUTHORIZED");
    });
  });

  describe("POST /api/v1/organizations", () => {
    it("should create new organization", async () => {
      const organizationData = {
        name: "New Test Organization",
        description: "A test organization",
        industry: "Technology",
        size: "1-10",
        timezone: "America/New_York",
        currency: "USD",
      };

      const response = await testClient
        .post("/api/v1/organizations")
        .set(authHeaders)
        .send(organizationData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(organizationData.name);
      expect(response.body.data.userRole).toBe("owner");
    });

    it("should validate required fields", async () => {
      const response = await testClient
        .post("/api/v1/organizations")
        .set(authHeaders)
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("VALIDATION_ERROR");
      expect(response.body.error.details.fields.name).toBeDefined();
    });

    it("should require internal user type", async () => {
      const clientUser = await createTestUser({
        email: "<EMAIL>",
        name: "Client User",
        userType: "client",
      });
      const clientHeaders = await getAuthHeaders(clientUser.user.id);

      const response = await testClient
        .post("/api/v1/organizations")
        .set(clientHeaders)
        .send({ name: "Test Org" });

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("FORBIDDEN");
    });
  });
});
```

### Performance Testing

**File: `tests/performance/load-test.ts`**

```typescript
import { describe, it, expect } from "vitest";
import { testClient } from "../utils/test-client";
import { createTestUser, getAuthHeaders } from "../setup";

describe("Performance Tests", () => {
  it("should handle concurrent organization requests", async () => {
    const testUser = await createTestUser({
      email: "<EMAIL>",
      name: "Performance Test User",
    });
    const authHeaders = await getAuthHeaders(testUser.user.id);

    const startTime = Date.now();
    const concurrentRequests = 10;

    const promises = Array.from({ length: concurrentRequests }, () =>
      testClient.get("/api/v1/organizations").set(authHeaders)
    );

    const responses = await Promise.all(promises);
    const endTime = Date.now();
    const duration = endTime - startTime;

    // All requests should succeed
    responses.forEach((response) => {
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    // Should complete within reasonable time (adjust threshold as needed)
    expect(duration).toBeLessThan(5000); // 5 seconds

    console.log(
      `${concurrentRequests} concurrent requests completed in ${duration}ms`
    );
  });

  it("should handle pagination efficiently", async () => {
    const testUser = await createTestUser({
      email: "<EMAIL>",
      name: "Pagination Test User",
    });
    const authHeaders = await getAuthHeaders(testUser.user.id);

    // Create multiple organizations for pagination testing
    const organizationPromises = Array.from({ length: 50 }, (_, i) =>
      testClient
        .post("/api/v1/organizations")
        .set(authHeaders)
        .send({ name: `Test Organization ${i + 1}` })
    );

    await Promise.all(organizationPromises);

    const startTime = Date.now();

    // Test different page sizes
    const pageSizes = [10, 20, 50];

    for (const pageSize of pageSizes) {
      const response = await testClient
        .get(`/api/v1/organizations?limit=${pageSize}&page=1`)
        .set(authHeaders);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(Math.min(pageSize, 50));
      expect(response.body.meta.pagination.limit).toBe(pageSize);
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`Pagination tests completed in ${duration}ms`);
    expect(duration).toBeLessThan(3000); // 3 seconds
  });
});
```

### CI/CD Pipeline Configuration

**File: `.github/workflows/test.yml`**

```yaml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Setup test environment
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          BETTER_AUTH_SECRET: test-secret-key-for-ci-testing-only
          BETTER_AUTH_URL: http://localhost:3000
          NEXT_PUBLIC_BETTER_AUTH_URL: http://localhost:3000
          NEXT_PUBLIC_APP_URL: http://localhost:3000
          NEXT_PUBLIC_API_URL: http://localhost:3000/api/v1
          NODE_ENV: test
        run: |
          npm run validate-config
          npm run db:generate
          npm run db:migrate

      - name: Run type checking
        run: npm run type-check

      - name: Run linting
        run: npm run lint

      - name: Run unit tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          BETTER_AUTH_SECRET: test-secret-key-for-ci-testing-only
          BETTER_AUTH_URL: http://localhost:3000
          NODE_ENV: test
        run: npm run test:unit

      - name: Run integration tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          BETTER_AUTH_SECRET: test-secret-key-for-ci-testing-only
          BETTER_AUTH_URL: http://localhost:3000
          NODE_ENV: test
        run: npm run test:integration

      - name: Generate test coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          BETTER_AUTH_SECRET: test-secret-key-for-ci-testing-only
          BETTER_AUTH_URL: http://localhost:3000
          NODE_ENV: test
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
```

## Definition of Done

- [ ] Complete OpenAPI specification covers all API endpoints
- [ ] Interactive API documentation is accessible and functional
- [ ] Authentication and authorization are properly documented
- [ ] Unit tests cover all API endpoints with >90% coverage
- [ ] Integration tests validate complex workflows
- [ ] Performance tests establish baseline metrics
- [ ] Security tests validate authentication and authorization
- [ ] Multi-tenant isolation is thoroughly tested
- [ ] CI/CD pipeline runs all tests automatically
- [ ] Test coverage reporting is integrated
- [ ] Error scenarios are comprehensively tested
- [ ] API response consistency is validated
- [ ] Data validation tests prevent invalid data processing
- [ ] Load testing establishes performance benchmarks
- [ ] Documentation includes usage examples and best practices
